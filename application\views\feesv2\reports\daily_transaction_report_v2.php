<?php
/**
 * Daily Transaction Report - Modular Version
 * 
 * This is an example implementation using the new modular report components
 */

// Set report-specific variables
$title = 'Daily Transaction Report';
$back_url = site_url('feesv2/fees_dashboard');
$show_export_buttons = true;
$filter_type = 'daily_transaction';
$report_type = 'daily_transaction';
$controller_url = 'feesv2/reports/';

// Report endpoints configuration
$report_endpoints = [
    'summary' => 'generate_report_for_day_book_summary',
    'summary_with_parents' => 'generate_report_for_day_book_summary1',
    'detailed' => 'generate_report_for_day_bookV1',
    'installment' => 'generate_report_for_installment_wise_day_bookV1',
    'save_filters' => 'save_filters',
    'get_filters' => 'get_predefined_filters',
    'get_filter_by_id' => 'get_predefined_filters_by_id',
    'update_filters' => 'update_filters'
];
?>

<!-- Include Report Header Component -->
<?php $this->load->view('feesv2/reports/components/report_header', [
    'title' => $title,
    'back_url' => $back_url,
    'show_export_buttons' => $show_export_buttons
]); ?>

<!-- Include Saved Reports Row Component -->
<?php $this->load->view('feesv2/reports/components/report_saved', [
    'show_saved_reports' => true,
    'filter_collapse_id' => 'accTwoColTwo',
    'show_panel_id' => 'show'
]); ?>

<!-- Include Report Filters Component -->
<?php $this->load->view('feesv2/reports/components/report_filters', [
    'filter_type' => $filter_type,
    'classes' => $classes,
    'fee_blueprints' => $fee_blueprints,
    'payment_mode' => $payment_mode,
    'admission_type' => $admission_type,
    'additionalAmount' => $additionalAmount,
    'sales' => $sales,
    'admission' => $admission
]); ?>

<!-- Include DataTable Component -->
<?php $this->load->view('feesv2/reports/components/report_datatable'); ?>

<!-- Include Report Script Component -->
<?php $this->load->view('feesv2/reports/components/report_script', [
    'report_type' => $report_type,
    'controller_url' => $controller_url,
    'report_endpoints' => $report_endpoints
]); ?>

<!-- Additional CSS for this specific report -->
<style type="text/css">
/* Daily Transaction Report Specific Styles */
.checkbox-inline {
    margin-right: 15px;
    font-size: 12px;
    font-weight: 600;
}

.checkbox-inline input[type="checkbox"] {
    margin-right: 5px;
    width: 14px;
    height: 14px;
}

.radio-inline {
    margin-right: 15px;
    font-size: 12px;
    font-weight: 600;
}

.radio-inline input[type="radio"] {
    margin-right: 5px;
}

/* Report specific table styling */
.day_book_summary table {
    font-size: 12px;
}

.day_book_summary th {
    background-color: #f8f9fa;
    font-weight: 600;
    text-align: center;
    padding: 8px;
}

.day_book_summary td {
    padding: 6px;
    border: 1px solid #dee2e6;
}

/* Slider styling */
input[type='range'] {
    margin: 0 auto;
    width: 100px;
}

#sliderDiv {
    text-align: center;
    width: 350px;
    float: right;
}

/* Select2 styling for multi-select */
.select2-checkbox-option {
    display: flex;
    align-items: start;
}

.select2-checkbox {
    margin-right: 5px;
}

/* Print specific styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .panel-heading,
    .btn,
    .more-menu {
        display: none !important;
    }
    
    .panel-body {
        padding: 0 !important;
    }
    
    table {
        font-size: 10px !important;
    }
    
    th, td {
        padding: 4px !important;
    }
}

/* Loading animation */
.loading-icon {
    padding: 40px;
}

.loading-icon .fa-spinner {
    color: #007bff;
}

/* Responsive design */
@media (max-width: 768px) {
    .checkbox-inline,
    .radio-inline {
        display: block;
        margin-bottom: 10px;
        margin-right: 0;
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 10px;
    }
    
    #sliderDiv {
        width: 100%;
        float: none;
        margin-bottom: 15px;
    }
}
</style>

<!-- Additional JavaScript for Daily Transaction Report -->
<script type="text/javascript">
// Daily Transaction Report specific functions

// Override report title
$(document).ready(function() {
    $('#report_title').text('<?php echo $title; ?>');
    $('#summary_report_title').text('<?php echo $title; ?>');
});

// Construct summary table function (specific to daily transaction)
function construct_summary_table(daily_tx) {
    var html = '<table class="table table-bordered table-striped" id="summary-table">';
    html += '<thead><tr>';
    html += '<th>Payment Type</th>';
    html += '<th>Total Payment</th>';
    html += '<th>Cash Payment</th>';
    html += '<th>Cheque Payment</th>';
    html += '<th>Online Payment</th>';
    html += '</tr></thead><tbody>';
    
    var totalPayment = 0;
    var totalCash = 0;
    var totalCheque = 0;
    var totalOnline = 0;
    
    for (var i = 0; i < daily_tx.length; i++) {
        var row = daily_tx[i];
        html += '<tr>';
        html += '<td>' + (row.payment_type || 'N/A') + '</td>';
        html += '<td class="text-right">₹' + (parseFloat(row.total_payment || 0).toFixed(2)) + '</td>';
        html += '<td class="text-right">₹' + (parseFloat(row.cash_payment || 0).toFixed(2)) + '</td>';
        html += '<td class="text-right">₹' + (parseFloat(row.cheque_payment || 0).toFixed(2)) + '</td>';
        html += '<td class="text-right">₹' + (parseFloat(row.online_payment || 0).toFixed(2)) + '</td>';
        html += '</tr>';
        
        totalPayment += parseFloat(row.total_payment || 0);
        totalCash += parseFloat(row.cash_payment || 0);
        totalCheque += parseFloat(row.cheque_payment || 0);
        totalOnline += parseFloat(row.online_payment || 0);
    }
    
    // Add total row
    html += '<tr class="table-info font-weight-bold">';
    html += '<td><strong>Total</strong></td>';
    html += '<td class="text-right"><strong>₹' + totalPayment.toFixed(2) + '</strong></td>';
    html += '<td class="text-right"><strong>₹' + totalCash.toFixed(2) + '</strong></td>';
    html += '<td class="text-right"><strong>₹' + totalCheque.toFixed(2) + '</strong></td>';
    html += '<td class="text-right"><strong>₹' + totalOnline.toFixed(2) + '</strong></td>';
    html += '</tr>';
    
    html += '</tbody></table>';
    
    return html;
}

// Handle details report for daily transaction
function handleDetailsReport(data) {
    if (data && data.trim() !== '') {
        $('.day_book').html(data);
        $('#sliderDiv').show();
        initializeDetailsDataTable();
    } else {
        showNoDataMessage();
    }
}

// Initialize details DataTable
function initializeDetailsDataTable() {
    if ($.fn.DataTable.isDataTable('#daily_dataTable')) {
        $('#daily_dataTable').DataTable().destroy();
    }
    
    $('#daily_dataTable').DataTable({
        dom: 'Blfrtip',
        scrollX: true,
        scrollY: 400,
        scrollCollapse: true,
        stateSave: true,
        autoWidth: false,
        fixedHeader: true,
        ordering: true,
        paging: true,
        pageLength: 25,
        responsive: true,
        buttons: [
            {
                extend: 'print',
                text: '<i class="fa fa-print"></i> Print',
                className: 'btn btn-info btn-sm'
            },
            {
                extend: 'excelHtml5',
                text: '<i class="fa fa-file-excel-o"></i> Excel',
                className: 'btn btn-success btn-sm'
            },
            {
                extend: 'colvis',
                text: '<i class="fa fa-columns"></i> Columns',
                className: 'btn btn-warning btn-sm'
            }
        ]
    });
}

// Saved filters functions (specific to daily transaction)
function get_predefined_filters() {
    $.ajax({
        url: '<?php echo site_url('feesv2/reports/get_predefined_filters'); ?>',
        type: 'post',
        data: {'report_type': 'daily_transaction'},
        success: function(data) {
            if (data) {
                var filters = JSON.parse(data);
                var options = '<option value="">Select Report</option>';
                for (var i = 0; i < filters.length; i++) {
                    options += '<option value="' + filters[i].id + '">' + filters[i].title + '</option>';
                }
                $('#filter_types').html(options);
            }
        }
    });
}

// Clear filters function
function clear_filters() {
    // Reset all form elements
    $('input[type="checkbox"]').prop('checked', false);
    $('input[type="radio"]').prop('checked', false);
    $('#type-2').prop('checked', true); // Default to Summary
    $('select').val('').trigger('change');
    $('input[type="text"], input[type="number"]').val('');
    
    // Reset date range to current month
    if (typeof moment !== 'undefined') {
        var startOfMonth = moment().startOf('month').format('DD-MM-YYYY');
        var endOfMonth = moment().endOf('month').format('DD-MM-YYYY');
        $('#from_date').val(startOfMonth);
        $('#to_date').val(endOfMonth);
        $('#reportrange span').html(moment().startOf('month').format('MMM D, YYYY') + ' - ' + moment().endOf('month').format('MMM D, YYYY'));
    }
    
    // Reset recon label
    $('#reconLable').html('Receipt');
    
    // Clear saved filter selection
    $('#filter_types').val('');
    $('#reload_filter').hide();
    
    // Clear report data
    $('.day_book_summary').html('');
    $('.day_book').html('');
    $('#print_visible').hide();
    $('#print_summary').hide();
}
</script>

<!-- Include required external libraries -->
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/moment.min.js') ?>"></script>
<script type="text/javascript" src="<?php echo base_url('assets/js/plugins/daterangepicker/daterangepicker.js') ?>"></script>
<link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css" rel="stylesheet" />
<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
