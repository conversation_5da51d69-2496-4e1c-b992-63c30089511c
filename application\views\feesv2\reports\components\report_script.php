<?php
/**
 * Reusable Report Script Component
 * 
 * This component handles the main report generation logic
 * 
 * Parameters:
 * - $report_type: Type of report (daily_transaction, fee_details, etc.)
 * - $controller_url: Base controller URL for AJAX calls
 * - $report_endpoints: Array of endpoint URLs for different report functions
 */

$report_type = isset($report_type) ? $report_type : 'daily_transaction';
$controller_url = isset($controller_url) ? $controller_url : 'feesv2/reports/';
$report_endpoints = isset($report_endpoints) ? $report_endpoints : [];

// Default endpoints for daily transaction report
if (empty($report_endpoints)) {
    $report_endpoints = [
        'summary' => 'generate_report_for_day_book_summary',
        'summary_with_parents' => 'generate_report_for_day_book_summary1',
        'detailed' => 'generate_report_for_day_bookV1',
        'installment' => 'generate_report_for_installment_wise_day_bookV1',
        'save_filters' => 'save_filters',
        'get_filters' => 'get_predefined_filters',
        'get_filter_by_id' => 'get_predefined_filters_by_id',
        'update_filters' => 'update_filters'
    ];
}
?>

<script type="text/javascript">
// Global variables for report functionality
var reportType = '<?php echo $report_type; ?>';
var controllerUrl = '<?php echo site_url($controller_url); ?>';
var reportEndpoints = <?php echo json_encode($report_endpoints); ?>;
var columns_activeArry = [];
var setColumnDef = [];
var summaryTable;
var detailsTable;

// Main report generation function
function getReport() {
    // Show loading
    showLoading();
    
    // Clear previous data
    clearPreviousData();
    
    // Disable search button
    $('#search').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> Loading...');
    
    // Get form data
    var formData = getFormData();
    
    // Determine URL based on report type and format
    var url = getReportUrl(formData);
    
    // Update date displays
    updateDateDisplays(formData.from_date, formData.to_date);
    
    // Make AJAX request
    $.ajax({
        url: url,
        type: 'POST',
        data: formData,
        success: function(response) {
            handleReportSuccess(response, formData);
        },
        error: function(xhr, status, error) {
            handleReportError(error);
        },
        complete: function() {
            // Re-enable search button
            $('#search').prop('disabled', false).html('<i class="fa fa-search"></i> Get Report');
            hideLoading();
        }
    });
}

// Get form data based on report type
function getFormData() {
    var baseData = {
        from_date: $('#from_date').val(),
        to_date: $('#to_date').val(),
        classId: $('#classId').val(),
        fee_type: $('#fee_type').val() || ['All']
    };
    
    // Add report-specific data
    switch(reportType) {
        case 'daily_transaction':
            return $.extend(baseData, {
                paymentModes: $('#paymentModes').val(),
                admission_type: $('#admission_type').val(),
                components: $('#components_include').is(':checked') ? 1 : 0,
                components_v_h: $('#components_v_h').is(':checked') ? 0 : 1,
                components_exclude: $('#components_exclude').is(':checked') ? 1 : 0,
                include_delete: $('#include_delete').is(':checked') ? 1 : 0,
                recon: $('#recon').is(':checked') ? 1 : 0,
                report_type: $('input[name="report_type"]:checked').val(),
                column_selections: $('#column_selections').val()
            });
            
        case 'fee_details':
            return $.extend(baseData, {
                student_status: $('#student_status').val(),
                fee_status: $('#fee_status').val(),
                amount_from: $('#amount_from').val(),
                amount_to: $('#amount_to').val()
            });
            
        default:
            return baseData;
    }
}

// Get report URL based on type and format
function getReportUrl(formData) {
    var endpoint = '';
    
    switch(reportType) {
        case 'daily_transaction':
            var reportFormat = formData.report_type || '2';
            switch(reportFormat) {
                case '2':
                    endpoint = reportEndpoints.summary;
                    break;
                case '3':
                    endpoint = reportEndpoints.summary_with_parents;
                    break;
                case '4':
                    endpoint = reportEndpoints.installment;
                    break;
                default:
                    endpoint = reportEndpoints.detailed;
            }
            break;
            
        case 'fee_details':
            endpoint = reportEndpoints.fee_details || 'generate_fee_details_report';
            break;
            
        default:
            endpoint = reportEndpoints.default || 'generate_report';
    }
    
    return controllerUrl + endpoint;
}

// Handle successful report response
function handleReportSuccess(data, formData) {
    try {
        var reportFormat = formData.report_type || '2';
        
        if (reportType === 'daily_transaction' && (reportFormat == '2' || reportFormat == '3')) {
            handleSummaryReport(data);
        } else {
            handleDetailedReport(data);
        }
        
        // Show print sections
        $('#print_visible').show();
        $('#print_summary').show();
        
    } catch (error) {
        console.error('Error processing report data:', error);
        handleReportError('Error processing report data');
    }
}

// Handle summary report
function handleSummaryReport(data) {
    try {
        var daily_tx = JSON.parse(data);
        
        if (daily_tx && daily_tx.length > 0) {
            $('.day_book_summary').html(construct_summary_table(daily_tx));
            initializeSummaryDataTable();
        } else {
            showNoDataMessage();
        }
    } catch (error) {
        console.error('Error parsing summary data:', error);
        showNoDataMessage();
    }
}

// Handle detailed report
function handleDetailedReport(data) {
    if (data && data.trim() !== '') {
        $('.day_book').html(data);
        $('#sliderDiv').show();
        initializeDetailsDataTable();
    } else {
        showNoDataMessage();
    }
}

// Handle report error
function handleReportError(error) {
    console.error('Report generation error:', error);
    
    var errorHtml = `
        <div class="alert alert-danger text-center">
            <i class="fa fa-exclamation-triangle fa-2x mb-3"></i>
            <h5>Error Generating Report</h5>
            <p class="mb-0">There was an error generating the report. Please try again or contact support if the problem persists.</p>
        </div>
    `;
    
    $('.day_book_summary').html(errorHtml);
    hideLoading();
}

// Update date displays
function updateDateDisplays(fromDate, toDate) {
    $('#fromDate').text(fromDate);
    $('#toDate').text(toDate);
    $('#fromDate_summary').text(fromDate);
    $('#toDate_summary').text(toDate);
}

// Clear previous data
function clearPreviousData() {
    $('.day_book').html('');
    $('.day_book_summary').html('');
    $('#print_visible').hide();
    $('#print_summary').hide();
    $('#sliderDiv').hide();
    $('#no-data-message').hide();
}

// Show loading indicator
function showLoading() {
    $('.loading-icon').show();
    $('#no-data-message').hide();
    $('.day_book_summary').html('');
}

// Hide loading indicator
function hideLoading() {
    $('.loading-icon').hide();
}

// Show no data message
function showNoDataMessage() {
    var noDataHtml = `
        <div class="alert alert-info text-center">
            <i class="fa fa-info-circle fa-2x mb-3"></i>
            <h5>No Data Found</h5>
            <p class="mb-0">No data found for the selected criteria. Please adjust your filters and try again.</p>
        </div>
    `;
    $('.day_book_summary').html(noDataHtml);
    hideLoading();
}

// Initialize Date Range Picker
function initializeDateRangePicker() {
    if (typeof moment !== 'undefined' && typeof $.fn.daterangepicker !== 'undefined') {
        $('#reportrange').daterangepicker({
            startDate: moment().startOf('month'),
            endDate: moment().endOf('month'),
            ranges: {
                'Today': [moment(), moment()],
                'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                'Last 7 Days': [moment().subtract(6, 'days'), moment()],
                'Last 30 Days': [moment().subtract(29, 'days'), moment()],
                'This Month': [moment().startOf('month'), moment().endOf('month')],
                'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
            }
        }, function(start, end, label) {
            $('#from_date').val(start.format('DD-MM-YYYY'));
            $('#to_date').val(end.format('DD-MM-YYYY'));
            $('#reportrange span').html(start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY'));
        });
        
        // Set initial values
        $('#from_date').val(moment().startOf('month').format('DD-MM-YYYY'));
        $('#to_date').val(moment().endOf('month').format('DD-MM-YYYY'));
        $('#reportrange span').html(moment().startOf('month').format('MMM D, YYYY') + ' - ' + moment().endOf('month').format('MMM D, YYYY'));
    } else {
        console.warn('Moment.js or DateRangePicker not loaded');
    }
}

// Initialize Multi-select dropdowns
function initializeMultiSelect() {
    if (typeof $.fn.select2 !== 'undefined') {
        $('.select').select2({
            placeholder: "Select options",
            allowClear: true,
            width: '100%'
        });
    } else if (typeof $.fn.selectpicker !== 'undefined') {
        $('.select').selectpicker({
            noneSelectedText: 'Select options',
            selectAllText: 'Select All',
            deselectAllText: 'Deselect All'
        });
    } else {
        console.warn('Select2 or Bootstrap Select not loaded');
    }
}

// Alias for backward compatibility
function get_daily_tx_report() {
    getReport();
}

// Document ready initialization
$(document).ready(function() {
    // Initialize components
    initializeDateRangePicker();
    initializeMultiSelect();
    
    // Load predefined filters
    get_predefined_filters();
    
    // Initialize date picker for existing elements
    if (typeof $.fn.datetimepicker !== 'undefined') {
        $('.date').datetimepicker({
            viewMode: 'days',
            format: 'DD-MM-YYYY'
        });
    }
});
</script>
